using System.Collections;
using System.Collections.Generic;
using Avalon.NPC.Enemies;
using Avalon.Simulation.Movement;
using UnityEngine;
using Unity.Entities;
using Unity.Mathematics;
using Unity.Deterministic.Mathematics;
using Unity.Transforms;
using Avalon.Simulation.SiegeSlots;

namespace FlowField.Tests
{
    /// <summary>
    /// Complex movement test scenario for FlowField system
    /// Creates a large 250x250 grid with obstacles, multiple targets, and periodic unit spawning
    /// </summary>
    public class ComplexMovementTestScenario : MonoBehaviour
    {
        [Header("Grid Configuration")]
        [SerializeField] private int2 gridSize = new int2(100, 100);
        [SerializeField] private dfloat2 cellSize = new dfloat2(dfloat.One, dfloat.One);
        [SerializeField] private dfloat2 worldOrigin = dfloat2.zero;

        [Header("Unit Spawning")]
        [SerializeField] private float spawnInterval = 2.0f;
        [SerializeField] private int maxUnitsPerSpawner = 50;
        [SerializeField] private int spawnerCount = 8;

        [Header("Targets")]
        [SerializeField] private int targetCount = 6;
        [SerializeField] private float targetMoveSpeed = 2.0f;
        [SerializeField] private bool movingTargets = true;

        [Header("Obstacles")]
        [SerializeField] private int obstacleGroupCount = 15;
        [SerializeField] private int2 obstacleGroupSize = new int2(8, 8);

        [Header("Visualization")]
        [SerializeField] private bool enableVisualization = true;
        [SerializeField] private bool enableGridVisualization = true;
        [SerializeField] private bool enableUnitVisualization = true;
        [SerializeField] private bool enableTargetVisualization = true;
        [SerializeField] private bool enableUnitPathVisualization = false;
        [SerializeField] private Material unitMaterial;
        [SerializeField] private Material targetMaterial;
        [SerializeField] private Material obstacleMaterial;

        // Visualization components
        private FlowField.Visualization.GridVisualization gridVisualization;
        private FlowField.Visualization.UnitVisualization unitVisualization;
        private FlowField.Visualization.TargetVisualization targetVisualization;
        private FlowField.Visualization.UnitPathVisualization unitPathVisualization;

        // ECS Components
        private World world;
        private EntityManager entityManager;
        private List<Entity> flowFieldEntities = new List<Entity>();
        private List<Entity> targetEntities = new List<Entity>();
        private List<Entity> spawnedUnits = new List<Entity>();
        private List<Vector3> spawnerPositions = new List<Vector3>();

        // Spawning state
        private float lastSpawnTime;
        private int currentSpawnerIndex = 0;
        private Dictionary<int, int> spawnerUnitCounts = new Dictionary<int, int>();

        // Target movement
        private List<Vector3> targetDirections = new List<Vector3>();
        private List<float> targetSpeeds = new List<float>();

        void Start()
        {
            InitializeECS();
            CreateLargeGrid();
            CreateObstacles();
            CreateTargets();
            SetupSpawners();
            StartCoroutine(PeriodicUnitSpawning());

            if (movingTargets)
            {
                StartCoroutine(MoveTargets());
            }

            // Initialize new visualization system
            if (enableVisualization)
            {
                InitializeVisualizationComponents();
            }

            // Initialize siege slot debug visualization
            InitializeSiegeSlotDebugVisualization();

            Debug.Log($"Complex Movement Test Scenario initialized with {gridSize.x}x{gridSize.y} grid");
            Debug.Log($"Spawners: {spawnerCount}, Targets: {targetCount}, Obstacle Groups: {obstacleGroupCount}");
            Debug.Log($"Flow Field Entities Created: {flowFieldEntities.Count}, Target Entities Created: {targetEntities.Count}");
        }

        private void InitializeECS()
        {
            world = World.DefaultGameObjectInjectionWorld;
            entityManager = world.EntityManager;
            
            EnemyFactory.Initialize(entityManager);
        }

        private void CreateLargeGrid()
        {
            // Create multiple flow field grids for different movement types
            var movementTypes = new MovementType[]
            {
                MovementType.Ground,
                MovementType.Amphibious,
                MovementType.Flying
            };

            // Only create flow field entities for the number of targets we'll have
            // This prevents unused flow fields from being created when targetCount < movementTypes.Length
            int flowFieldCount = math.min(targetCount, movementTypes.Length);

            for (int i = 0; i < flowFieldCount; i++)
            {
                var flowFieldEntity = entityManager.CreateEntity();

                // Add FlowFieldGrid component
                var flowFieldGrid = FlowFieldUtils.CreateFlowFieldGrid(
                    gridSize,
                    cellSize,
                    worldOrigin,
                    Entity.Null, // Will be set when targets are created
                    i + 1, // Target ID
                    movementTypes[i % movementTypes.Length] // Cycle through movement types if we have more targets than types
                );

                entityManager.AddComponentData(flowFieldEntity, flowFieldGrid);

                // Add flow field cell buffer
                var buffer = entityManager.AddBuffer<FlowFieldCellBuffer>(flowFieldEntity);
                InitializeFlowFieldCells(buffer, gridSize, movementTypes[i % movementTypes.Length]);

                flowFieldEntities.Add(flowFieldEntity);
            }
        }

        private void InitializeFlowFieldCells(DynamicBuffer<FlowFieldCellBuffer> buffer, int2 gridSize, MovementType movementType)
        {
            buffer.ResizeUninitialized(gridSize.x * gridSize.y);
            
            for (int y = 0; y < gridSize.y; y++)
            {
                for (int x = 0; x < gridSize.x; x++)
                {
                    var index = y * gridSize.x + x;
                    var cell = new FlowFieldCell();

                    // Initialize as walkable for all movement types initially
                    cell.SetWalkable(MovementType.Ground, true);
                    cell.SetWalkable(MovementType.Amphibious, true);
                    cell.SetWalkable(MovementType.Flying, true);
                    cell.SetWalkable(MovementType.Heavy, true);

                    cell.cost = dfloat.One;
                    cell.distance = dfloat.Zero;
                    cell.direction = dfloat2.zero;
                    cell.isTarget = false;
                    
                    buffer[index] = new FlowFieldCellBuffer { cell = cell };
                }
            }
        }

        private void CreateObstacles()
        {
            var random = new Unity.Mathematics.Random(12345);
            
            for (int group = 0; group < obstacleGroupCount; group++)
            {
                // Random position for obstacle group
                var groupCenter = new int2(
                    random.NextInt(obstacleGroupSize.x, gridSize.x - obstacleGroupSize.x),
                    random.NextInt(obstacleGroupSize.y, gridSize.y - obstacleGroupSize.y)
                );

                // Create obstacle pattern (random shape)
                CreateObstacleGroup(groupCenter, obstacleGroupSize, random);
            }
        }

        private void CreateObstacleGroup(int2 center, int2 size, Unity.Mathematics.Random random)
        {
            for (int y = 0; y < size.y; y++)
            {
                for (int x = 0; x < size.x; x++)
                {
                    var pos = center + new int2(x - size.x / 2, y - size.y / 2);
                    
                    // Create interesting obstacle patterns
                    bool shouldCreateObstacle = false;
                    
                    // Create different patterns based on group index
                    var pattern = random.NextInt(0, 4);
                    switch (pattern)
                    {
                        case 0: // Solid rectangle
                            shouldCreateObstacle = true;
                            break;
                        case 1: // Hollow rectangle
                            shouldCreateObstacle = (x == 0 || x == size.x - 1 || y == 0 || y == size.y - 1);
                            break;
                        case 2: // Cross pattern
                            shouldCreateObstacle = (x == size.x / 2 || y == size.y / 2);
                            break;
                        case 3: // Random scattered
                            shouldCreateObstacle = random.NextFloat() < 0.6f;
                            break;
                    }

                    if (shouldCreateObstacle && IsValidPosition(pos))
                    {
                        CreateObstacleAtPosition(pos);
                    }
                }
            }
        }

        private bool IsValidPosition(int2 pos)
        {
            return pos.x >= 0 && pos.x < gridSize.x && pos.y >= 0 && pos.y < gridSize.y;
        }

        private void CreateObstacleAtPosition(int2 gridPos)
        {
            // Set obstacle in all flow field grids
            foreach (var flowFieldEntity in flowFieldEntities)
            {
                var buffer = entityManager.GetBuffer<FlowFieldCellBuffer>(flowFieldEntity);
                var flowFieldGrid = entityManager.GetComponentData<FlowFieldGrid>(flowFieldEntity);
                
                // Ground units can't pass through obstacles
                FlowFieldUtils.SetObstacle(buffer, gridPos, gridSize, MovementType.Ground, false);

                // Amphibious units can't pass through land obstacles
                FlowFieldUtils.SetObstacle(buffer, gridPos, gridSize, MovementType.Amphibious, false);

                // Heavy units definitely can't pass through obstacles
                FlowFieldUtils.SetObstacle(buffer, gridPos, gridSize, MovementType.Heavy, false);

                // Flying units can pass over most obstacles (except tall ones)
                // For this test, let some obstacles block flying units too
                if (UnityEngine.Random.value < 0.3f) // 30% of obstacles block flying
                {
                    FlowFieldUtils.SetObstacle(buffer, gridPos, gridSize, MovementType.Flying, false);
                }
            }

            // Create visual representation if enabled
            if (enableVisualization && obstacleMaterial != null)
            {
                CreateObstacleVisual(gridPos);
            }
        }

        private void CreateObstacleVisual(int2 gridPos)
        {
            var worldPos = new Vector3(
                gridPos.x * (float)cellSize.x + (float)worldOrigin.x,
                0.5f,
                gridPos.y * (float)cellSize.y + (float)worldOrigin.y
            );

            var obstacle = GameObject.CreatePrimitive(PrimitiveType.Cube);
            obstacle.transform.position = worldPos;
            obstacle.transform.localScale = new Vector3((float)cellSize.x * 0.9f, 1.0f, (float)cellSize.y * 0.9f);
            obstacle.name = $"Obstacle_{gridPos.x}_{gridPos.y}";
            
            if (obstacleMaterial != null)
            {
                obstacle.GetComponent<Renderer>().material = obstacleMaterial;
            }
        }

        private void CreateTargets()
        {
            var random = new Unity.Mathematics.Random(54321);

            Debug.Log($"Creating {targetCount} targets with {flowFieldEntities.Count} flow field entities available");

            for (int i = 0; i < targetCount; i++)
            {
                // Create target entity
                var targetEntity = entityManager.CreateEntity();

                // Random position around the edges of the grid
                Vector3 targetPos = GetRandomEdgePosition(random);

                // Add transform components for deterministic simulation and smooth interpolation
                entityManager.AddComponentData(targetEntity, new SimulationTransform
                {
                    position = new dfloat3((dfloat)targetPos.x, (dfloat)targetPos.y, (dfloat)targetPos.z),
                    rotation = new dquaternion(dfloat.Zero, dfloat.Zero, dfloat.Zero, dfloat.One),
                    scale = dfloat.One
                });

                // Add previous transform for interpolation (DETERMINISTIC)
                entityManager.AddComponentData(targetEntity, new PreviousSimulationTransform
                {
                    Position = new dfloat3((dfloat)targetPos.x, (dfloat)targetPos.y, (dfloat)targetPos.z),
                    Rotation = new dquaternion(dfloat.Zero, dfloat.Zero, dfloat.Zero, dfloat.One),
                    Scale = dfloat.One
                });

                entityManager.AddComponentData(targetEntity, new LocalTransform
                {
                    Position = targetPos,
                    Rotation = quaternion.identity,
                    Scale = 1.0f
                });

                // Add target identifier
                entityManager.AddComponentData(targetEntity, new FlowFieldTarget
                {
                    targetId = i + 1,
                    isActive = true
                });

                // Add siege slot data for organized enemy positioning
                var siegeSlotData = SiegeSlotData.CreateDefault(6, new dfloat(2.0f));
                entityManager.AddComponentData(targetEntity, siegeSlotData);

                targetEntities.Add(targetEntity);

                // Update flow field grids to point to this target
                // Use modulo to cycle through available flow fields if we have more targets than flow fields
                if (flowFieldEntities.Count > 0)
                {
                    int flowFieldIndex = i % flowFieldEntities.Count;
                    var flowFieldGrid = entityManager.GetComponentData<FlowFieldGrid>(flowFieldEntities[flowFieldIndex]);
                    flowFieldGrid.targetEntity = targetEntity;
                    flowFieldGrid.needsUpdate = true;
                    entityManager.SetComponentData(flowFieldEntities[flowFieldIndex], flowFieldGrid);

                    Debug.Log($"Target {i + 1} assigned to flow field {flowFieldIndex} (movement type: {flowFieldGrid.movementType})");
                }

                // Setup target movement
                if (movingTargets)
                {
                    targetDirections.Add(GetRandomDirection(random));
                    targetSpeeds.Add(targetMoveSpeed * random.NextFloat(0.5f, 1.5f));
                }

                // Create visual representation
                if (enableVisualization)
                {
                    CreateTargetVisual(targetPos, i);
                }
            }
        }

        private Vector3 GetRandomEdgePosition(Unity.Mathematics.Random random)
        {
            var edge = random.NextInt(0, 4); // 0=top, 1=right, 2=bottom, 3=left
            var worldWidth = gridSize.x * (float)cellSize.x;
            var worldHeight = gridSize.y * (float)cellSize.y;

            switch (edge)
            {
                case 0: // Top edge
                    return new Vector3(
                        random.NextFloat(0, worldWidth),
                        1.0f,
                        worldHeight - 5.0f
                    );
                case 1: // Right edge
                    return new Vector3(
                        worldWidth - 5.0f,
                        1.0f,
                        random.NextFloat(0, worldHeight)
                    );
                case 2: // Bottom edge
                    return new Vector3(
                        random.NextFloat(0, worldWidth),
                        1.0f,
                        5.0f
                    );
                case 3: // Left edge
                    return new Vector3(
                        5.0f,
                        1.0f,
                        random.NextFloat(0, worldHeight)
                    );
                default:
                    return new Vector3(worldWidth / 2, 1.0f, worldHeight / 2);
            }
        }

        private Vector3 GetRandomDirection(Unity.Mathematics.Random random)
        {
            var angle = random.NextFloat(0, 2 * math.PI);
            return new Vector3(math.cos(angle), 0, math.sin(angle));
        }

        private void CreateTargetVisual(Vector3 position, int targetIndex)
        {
            var target = GameObject.CreatePrimitive(PrimitiveType.Sphere);
            target.transform.position = position;
            target.transform.localScale = Vector3.one * 2.0f;
            target.name = $"Target_{targetIndex}";

            if (targetMaterial != null)
            {
                target.GetComponent<Renderer>().material = targetMaterial;
            }

            // Add a light to make targets more visible
            var light = target.AddComponent<Light>();
            light.type = LightType.Point;
            light.range = 10.0f;
            light.intensity = 2.0f;
            light.color = Color.HSVToRGB((float)targetIndex / targetCount, 1.0f, 1.0f);
        }

        private void SetupSpawners()
        {
            var random = new Unity.Mathematics.Random(98765);

            for (int i = 0; i < spawnerCount; i++)
            {
                // Create spawner positions around the grid
                Vector3 spawnerPos = GetSpawnerPosition(i, random);
                spawnerPositions.Add(spawnerPos);
                spawnerUnitCounts[i] = 0;

                // Create visual marker for spawner
                if (enableVisualization)
                {
                    CreateSpawnerVisual(spawnerPos, i);
                }
            }
        }

        private Vector3 GetSpawnerPosition(int spawnerIndex, Unity.Mathematics.Random random)
        {
            var worldWidth = gridSize.x * (float)cellSize.x;
            var worldHeight = gridSize.y * (float)cellSize.y;

            // Distribute spawners around the perimeter
            var angle = (float)spawnerIndex / spawnerCount * 2 * math.PI;
            var radius = math.min(worldWidth, worldHeight) * 0.4f;

            var centerX = worldWidth / 2;
            var centerY = worldHeight / 2;

            return new Vector3(
                centerX + radius * math.cos(angle),
                0.5f,
                centerY + radius * math.sin(angle)
            );
        }

        private void CreateSpawnerVisual(Vector3 position, int spawnerIndex)
        {
            var spawner = GameObject.CreatePrimitive(PrimitiveType.Cylinder);
            spawner.transform.position = position;
            spawner.transform.localScale = new Vector3(2.0f, 0.5f, 2.0f);
            spawner.name = $"Spawner_{spawnerIndex}";

            var renderer = spawner.GetComponent<Renderer>();
            renderer.material.color = Color.green;
        }

        private IEnumerator PeriodicUnitSpawning()
        {
            while (true)
            {
                yield return new WaitForSeconds(spawnInterval);

                if (EnemyDefinition.Database.Count > 0)
                {
                    SpawnUnitAtCurrentSpawner();
                    currentSpawnerIndex = (currentSpawnerIndex + 1) % spawnerCount;
                }
            }
        }

        private void SpawnUnitAtCurrentSpawner()
        {
            if (spawnerUnitCounts[currentSpawnerIndex] >= maxUnitsPerSpawner)
            {
                return; // This spawner has reached its limit
            }

            var spawnerPos = spawnerPositions[currentSpawnerIndex];
            var unitTypeDefinitionId = UnityEngine.Random.Range(0, EnemyDefinition.Database.Count) + 1;
            var unitType = EnemyDefinition.Database.GetById(unitTypeDefinitionId);

            // Create unit entity
            var unitEntity = entityManager.CreateEntity();
            var targetId = UnityEngine.Random.Range(1, targetCount + 1);
            entityManager.AddComponentData(unitEntity, new EnemySpawnRequest
            {
                EnemyDefinitionId = unitTypeDefinitionId,
                Position = new dfloat3((dfloat) spawnerPos.x, (dfloat) spawnerPos.y, (dfloat) spawnerPos.z),
                TargetId = targetId
            });

            spawnedUnits.Add(unitEntity);
            spawnerUnitCounts[currentSpawnerIndex]++;

            Debug.Log($"Spawned {unitType.Name} at spawner {currentSpawnerIndex}, targeting {targetId}");
        }
        
        private IEnumerator MoveTargets()
        {
            while (true)
            {
                yield return new WaitForSeconds(10f); // Update target positions frequently

                for (int i = 0; i < targetEntities.Count && i < targetDirections.Count; i++)
                {
                    MoveTarget(i);
                }
            }
        }

        private void MoveTarget(int targetIndex)
        {
            if (targetIndex >= targetEntities.Count) return;

            var targetEntity = targetEntities[targetIndex];
            var currentTransform = entityManager.GetComponentData<SimulationTransform>(targetEntity);
            var currentPos = currentTransform.position;

            // Move target
            var movement = new dfloat3(
                (dfloat)(targetDirections[targetIndex].x * targetSpeeds[targetIndex] * Time.deltaTime),
                (dfloat)(targetDirections[targetIndex].y * targetSpeeds[targetIndex] * Time.deltaTime),
                (dfloat)(targetDirections[targetIndex].z * targetSpeeds[targetIndex] * Time.deltaTime)
            );
            var newPos = currentPos + movement;

            // Bounce off boundaries
            var worldWidth = gridSize.x * (float)cellSize.x;
            var worldHeight = gridSize.y * (float)cellSize.y;
            var bounced = false;

            if (newPos.x < (dfloat)5 || newPos.x > worldWidth - (dfloat)5)
            {
                targetDirections[targetIndex] = new Vector3(-targetDirections[targetIndex].x, 0, targetDirections[targetIndex].z);
                bounced = true;
            }

            if (newPos.z < (dfloat)5 || newPos.z > worldHeight - (dfloat)5)
            {
                targetDirections[targetIndex] = new Vector3(targetDirections[targetIndex].x, 0, -targetDirections[targetIndex].z);
                bounced = true;
            }

            if (bounced)
            {
                movement = new dfloat3(
                    (dfloat)(targetDirections[targetIndex].x * targetSpeeds[targetIndex] * Time.deltaTime),
                    (dfloat)(targetDirections[targetIndex].y * targetSpeeds[targetIndex] * Time.deltaTime),
                    (dfloat)(targetDirections[targetIndex].z * targetSpeeds[targetIndex] * Time.deltaTime)
                );
                newPos = currentPos + movement;
            }

            // Update entity transform (DETERMINISTIC)
            // Only update SimulationTransform - the InterpolationSystem will handle updating LocalTransform
            entityManager.SetComponentData(targetEntity, new SimulationTransform
            {
                position = newPos,
                rotation = currentTransform.rotation,
                scale = currentTransform.scale
            });

            // Mark flow field for update
            if (targetIndex < flowFieldEntities.Count)
            {
                var flowFieldGrid = entityManager.GetComponentData<FlowFieldGrid>(flowFieldEntities[targetIndex]);
                flowFieldGrid.needsUpdate = true;
                entityManager.SetComponentData(flowFieldEntities[targetIndex], flowFieldGrid);
            }
        }

        void Update()
        {
            // Display statistics
            if (Time.frameCount % 60 == 0) // Every second at 60 FPS
            {
                DisplayStatistics();
            }
        }

        private void DisplayStatistics()
        {
            var totalUnits = spawnedUnits.Count;
            var activeUnits = 0;

            foreach (var unit in spawnedUnits)
            {
                if (entityManager.Exists(unit))
                {
                    activeUnits++;
                }
            }

            Debug.Log($"Movement Test Stats - Total Spawned: {totalUnits}, Active: {activeUnits}, Targets: {targetEntities.Count}");
        }

        void OnDestroy()
        {
            // Cleanup spawned entities
            foreach (var unit in spawnedUnits)
            {
                if (entityManager.Exists(unit))
                {
                    entityManager.DestroyEntity(unit);
                }
            }

            foreach (var target in targetEntities)
            {
                if (entityManager.Exists(target))
                {
                    entityManager.DestroyEntity(target);
                }
            }

            foreach (var flowField in flowFieldEntities)
            {
                if (entityManager.Exists(flowField))
                {
                    entityManager.DestroyEntity(flowField);
                }
            }
        }

        /// <summary>
        /// Initialize the new modular visualization system
        /// </summary>
        private void InitializeVisualizationComponents()
        {
            // Create and configure Grid Visualization
            if (enableGridVisualization && flowFieldEntities.Count > 0)
            {
                gridVisualization = gameObject.AddComponent<Visualization.GridVisualization>();
                gridVisualization.Initialize(entityManager, world);
                gridVisualization.FlowFieldEntity = flowFieldEntities[0]; // Use first flow field
                gridVisualization.GridSize = gridSize;
                gridVisualization.CellSize = cellSize;
                gridVisualization.WorldOrigin = worldOrigin;
                gridVisualization.MovementType = MovementType.Ground;
                gridVisualization.IsEnabled = true;
            }

            // Create and configure Unit Visualization
            if (enableUnitVisualization)
            {
                unitVisualization = gameObject.AddComponent<Visualization.UnitVisualization>();
                unitVisualization.Initialize(entityManager, world);
                unitVisualization.UnitsToVisualize = spawnedUnits;
                unitVisualization.AutoFindUnits = true;
                unitVisualization.IsEnabled = true;

                if (targetEntities.Count > 0)
                {
                    unitVisualization.TargetEntity = targetEntities[0];
                }
            }

            // Create and configure Target Visualization
            if (enableTargetVisualization)
            {
                targetVisualization = gameObject.AddComponent<Visualization.TargetVisualization>();
                targetVisualization.Initialize(entityManager, world);
                targetVisualization.TargetsToVisualize = targetEntities;
                targetVisualization.AutoFindTargets = true;
                targetVisualization.IsEnabled = true;
            }

            // Create and configure Unit Path Visualization
            if (enableUnitPathVisualization)
            {
                unitPathVisualization = gameObject.AddComponent<Visualization.UnitPathVisualization>();
                unitPathVisualization.Initialize(entityManager, world);
                unitPathVisualization.UnitsToTrack = spawnedUnits;
                unitPathVisualization.AutoFindUnits = true;
                unitPathVisualization.IsEnabled = true;
            }

            Debug.Log("Modular visualization system initialized for Complex Movement Test Scenario");
        }

        private void InitializeSiegeSlotDebugVisualization()
        {
            // Ensure siege slot debug visualization is properly enabled
            var debugSettings = SiegeSlotDebugSettings.Instance;
            debugSettings.enableDebugVisualization = true;
            debugSettings.showSlotPositions = true;
            debugSettings.showEnemyAssignments = true;
            debugSettings.showStatistics = true;

            Debug.Log($"Siege Slot Debug Visualization initialized - Targets: {targetCount}");
            Debug.Log($"Debug settings: enableDebugVisualization={debugSettings.enableDebugVisualization}, showSlotPositions={debugSettings.showSlotPositions}");

            // Verify that targets have SiegeSlotData components
            foreach (var targetEntity in targetEntities)
            {
                if (entityManager.HasComponent<SiegeSlotData>(targetEntity))
                {
                    var siegeSlotData = entityManager.GetComponentData<SiegeSlotData>(targetEntity);
                    Debug.Log($"Target entity has SiegeSlotData: slotCount={siegeSlotData.slotCount}, radius={siegeSlotData.slotRadius}, isActive={siegeSlotData.isActive}");
                }
                else
                {
                    Debug.LogWarning($"Target entity is missing SiegeSlotData component!");
                }
            }
        }
    }
}
